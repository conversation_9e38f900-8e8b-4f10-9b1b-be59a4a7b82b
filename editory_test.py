import asyncio
import aiohttp
import json
from pymongo import AsyncMongoClient
from typing import Dict, Any, Optional

# Configuration
API_BASE_URL = "http://localhost:8204"
LOGIN_ENDPOINT = "/v1/auth/login"
GENERATE_ENDPOINT = "/v2/editor/generate"

# Database connection
db = "mongodb://localhost:27017/"
client = AsyncMongoClient(db)["test_nepali_app"]

# Login credentials - you may need to update these
LOGIN_CREDENTIALS = {
    "username": "admin",  # Update with actual credentials
    "password": "ipZT=8b50*bL",       # Update with actual credentials
    "client_id": "test"       # Update with actual client_id
}

async def login_and_get_token() -> Optional[str]:
    """
    Login to the API and get JWT token using OAuth2 form data format.

    Returns:
        JWT token string if successful, None otherwise
    """
    async with aiohttp.ClientSession() as session:
        try:
            login_url = f"{API_BASE_URL}{LOGIN_ENDPOINT}"
            print(f"🔐 Attempting login at: {login_url}")

            # Prepare form data for OAuth2 format
            form_data = aiohttp.FormData()
            form_data.add_field('username', LOGIN_CREDENTIALS['username'])
            form_data.add_field('password', LOGIN_CREDENTIALS['password'])
            form_data.add_field('client_id', LOGIN_CREDENTIALS['client_id'])

            async with session.post(
                login_url,
                data=form_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    token = data.get("access_token")
                    if token:
                        print("✅ Login successful!")
                        print(f"🔑 Token received (length: {len(token)} chars)")
                        return token
                    else:
                        print("❌ Login failed: No access token in response")
                        print(f"Response: {data}")
                        return None
                else:
                    error_text = await response.text()
                    print(f"❌ Login failed with status {response.status}")
                    print(f"Error: {error_text}")
                    return None
        except Exception as e:
            print(f"❌ Login error: {e}")
            return None

async def generate_content(token: str, theme_id: str, prompt: str, theme_name: str) -> Dict[str, Any]:
    """
    Call the /generate endpoint for a specific theme.

    Args:
        token: JWT authentication token
        theme_id: Theme ID string
        prompt: Content prompt for generation
        theme_name: Theme name for logging

    Returns:
        API response data
    """
    async with aiohttp.ClientSession() as session:
        try:
            generate_url = f"{API_BASE_URL}{GENERATE_ENDPOINT}"
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }

            payload = {
                "content": prompt,
                "theme_id": theme_id
            }

            print(f"🎯 Generating content for theme: {theme_name} (ID: {theme_id})")
            print(f"📝 Prompt length: {len(prompt)} characters")

            async with session.post(
                generate_url,
                json=payload,
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Generation successful for {theme_name}")
                    print(f"📊 Response keys: {list(data.keys())}")
                    return data
                else:
                    error_text = await response.text()
                    print(f"❌ Generation failed for {theme_name} with status {response.status}")
                    print(f"Error: {error_text}")
                    return {"error": f"HTTP {response.status}: {error_text}"}
        except Exception as e:
            print(f"❌ Generation error for {theme_name}: {e}")
            return {"error": str(e)}

async def main():
    print("🚀 Starting theme-based content generation...")

    # Step 1: Login and get token
    token = await login_and_get_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return

    # Step 2: Get themes from database
    print("📚 Fetching themes from database...")
    themes = await client.themes.find().to_list(length=None)
    print(f"Found {len(themes)} themes in database")

    # Step 3: Define story prompts for each theme
    story_prompts = [
        {
            "theme_id": "68623da16e5c620e70adb5c3",
            "prompt": "Write a very simple story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Animals' (जनावरहरू). The story should help children identify common animals and their sounds. Use simple, repetitive Nepali sentences and introduce core vocabulary like 'गाई' (cow), 'कुकुर' (dog), and 'बिरालो' (cat) along with their sounds ('मो.. मो..', 'भु-भु', 'म्याउ म्याउ'). The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "68623da16e5c620e70adb5d0",
            "prompt": "Create an interactive story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Body Parts' (शरीरका अंगहरू). The story should encourage a child to point to their own body parts. Use engaging questions like 'तिम्रो आँखा कहाँ छ?' and focus on Nepali vocabulary like 'आँखा' (eyes), 'नाक' (nose), 'हात' (hands), and 'खुट्टा' (feet). The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "68623da16e5c620e70adb5dd",
            "prompt": "Write a short, visual story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Colors' (रंगहरू). Follow a character who identifies the colors of everyday Nepali objects, such as a 'रातो स्याउ' (red apple), 'हरियो पात' (green leaf), and 'पहेँलो फूल' (yellow flower). Use simple sentence structures like 'हेर, स्याउ रातो छ।' The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "68623da16e5c620e70adb5ea",
            "prompt": "Write a heartwarming story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Family' (परिवार). Introduce the core family members using Nepali terms: 'आमा' (mother), 'बुबा' (father), 'दिदी' (older sister), and 'भाइ' (younger brother). The plot should be about a simple, loving activity they do together. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "68623da16e5c620e70adb5f7",
            "prompt": "Create a very simple story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Food' (खाना). The story should be about a child eating a typical Nepali meal, introducing words like 'भात' (rice), 'दाल' (lentils), and 'तरकारी' (vegetables). Emphasize that healthy food makes us strong. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a910",
            "prompt": "Write an energetic story in Nepali for a 5-8 year old child, suitable for difficulty level 2. The theme is 'Sports' (खेलकुद). The story should feature friends playing a popular sport in Nepal, like 'फुटबल' (football) or 'डन्डीबियो'. Describe the action, teamwork, and fun of the game using dynamic Nepali verbs. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a911",
            "prompt": "Write an adventurous story in Nepali for an 8-12 year old child, suitable for difficulty level 3. The theme is 'Geography' (भूगोल). Create a narrative about a journey across Nepal's diverse landscapes. Use rich Nepali vocabulary to describe the 'हिमाल' (Himalayas), 'पहाड' (hills), and 'तराई' (plains). Mention famous places like 'सगरमाथा' or 'फेवा ताल'. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a912",
            "prompt": "Create a captivating story in Nepali for an 8-12 year old child, suitable for difficulty level 3. The theme is 'History' (इतिहास). The story should narrate an event or the life of a famous figure from Nepali history, like 'अमर सिंह थापा' or 'भृकुटी', in an engaging way that highlights their bravery and importance to Nepal. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a913",
            "prompt": "Write a story in Nepali for a 5-10 year old child, suitable for difficulty level 2. The theme is 'Culture' (संस्कृति). The story should immerse the reader in a Nepali cultural tradition, like preparing for 'दशैं' by flying kites ('चङ्गा उडाउनु') or celebrating 'तिहार' with 'देउसी-भैलो'. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a914",
            "prompt": "Write a musical story in Nepali for a 3-7 year old child, suitable for difficulty level 2. The theme is 'Music' (संगीत). The story should introduce traditional Nepali musical instruments. Feature characters playing a 'मादल', 'बाँसुरी' (flute), and 'सारंगी' and describe the joyful sounds they make. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a915",
            "prompt": "Write a simple counting story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Numbers' (संख्या). The story should involve a character counting common objects from 'एक' (one) to 'दश' (ten), for example, 'एक सूर्य' (one sun), 'दुई चरा' (two birds). Use repetition for reinforcement. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a916",
            "prompt": "Create a fun story in Nepali for a 3-6 year old child, suitable for difficulty level 1. The theme is 'Alphabet' (वर्णमाला). The story can personify the Nepali letters. For example, 'क' finds a 'कमल' (lotus), and 'ख' chases a 'खरायो' (rabbit). Focus on the sound and a corresponding word for a few letters. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a917",
            "prompt": "Write a 'shape hunt' story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Shapes' (आकार). A character finds shapes in their surroundings: a 'गोलो' (round) roti, a 'त्रिकोण' (triangle) samosa, and a 'वर्ग' (square) window. Keep the language very simple and direct. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a918",
            "prompt": "Write an exciting story in Nepali for a 3-6 year old child, suitable for difficulty level 2. The theme is 'Vehicles' (सवारीसाधन). The story should follow a child observing different vehicles on a Nepali road, like a 'बस', 'ट्याक्सी', and 'मोटरसाइकल'. Include their sounds and simple descriptions of where they are going. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a919",
            "prompt": "Create an informative story in Nepali for a 4-7 year old child, suitable for difficulty level 2. The theme is 'Professions' (पेशा). A child meets people with different jobs common in Nepal, like a 'शिक्षक' (teacher), a 'डाक्टर' (doctor), and a 'किसान' (farmer), and learns how they each help the community. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a91a",
            "prompt": "Write a very basic story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Fruits' (फलफूल). Introduce common Nepali fruits like 'स्याउ' (apple), 'केरा' (banana), and 'सुन्तला' (orange). Describe their color and simple taste ('गुलियो' - sweet) with simple sentences. The focus is on identification. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a91b",
            "prompt": "Create a short story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Vegetables' (तरकारी). The story could be about a visit to a vegetable market ('तरकारी बजार') to identify vegetables like 'आलु' (potato), 'गोलभेडा' (tomato), and 'साग' (leafy greens). The message should be about eating healthy. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a91c",
            "prompt": "Write a gentle, descriptive story in Nepali for a 4-8 year old child, suitable for difficulty level 2. The theme is 'Nature' (प्रकृति). Describe a peaceful scene in the Nepali countryside, mentioning the 'रुख' (trees), 'फूल' (flowers), 'नदी' (river), and 'पहाड' (hills). The story should evoke a sense of calm and wonder. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a91d",
            "prompt": "Write a story in Nepali for a 3-6 year old child, suitable for difficulty level 2. The theme is 'Weather' (मौसम). Describe a day with changing weather: 'घाम लाग्यो' (it's sunny), 'बादल लाग्यो' (it's cloudy), and 'पानी पर्यो' (it's raining). Show what a child does in each weather, connecting it to Nepali life. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a91e",
            "prompt": "Create a simple story for a 3-6 year old child in Nepali, suitable for difficulty level 1. The theme is 'Clothes' (कपडा). The story can describe a child getting dressed, naming items like 'सर्ट' (shirt), 'पाइन्ट' (pants), and a 'टोपी' (hat). Connect the clothes to the weather or an occasion. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "68623da16e5c620e70adb5f7",
            "prompt": "Create a very simple story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Food' (खाना). The story should be about a child eating a typical Nepali meal, introducing words like 'भात' (rice), 'दाल' (lentils), and 'तरकारी' (vegetables). Emphasize that healthy food makes us strong. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a910",
            "prompt": "Write an energetic story in Nepali for a 5-8 year old child, suitable for difficulty level 2. The theme is 'Sports' (खेलकुद). The story should feature friends playing a popular sport in Nepal, like 'फुटबल' (football) or 'डन्डीबियो'. Describe the action, teamwork, and fun of the game using dynamic Nepali verbs. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a911",
            "prompt": "Write an adventurous story in Nepali for an 8-12 year old child, suitable for difficulty level 3. The theme is 'Geography' (भूगोल). Create a narrative about a journey across Nepal's diverse landscapes. Use rich Nepali vocabulary to describe the 'हिमाल' (Himalayas), 'पहाड' (hills), and 'तराई' (plains). Mention famous places like 'सगरमाथा' or 'फेवा ताल'. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a912",
            "prompt": "Create a captivating story in Nepali for an 8-12 year old child, suitable for difficulty level 3. The theme is 'History' (इतिहास). The story should narrate an event or the life of a famous figure from Nepali history, like 'अमर सिंह थापा' or 'भृकुटी', in an engaging way that highlights their bravery and importance to Nepal. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a913",
            "prompt": "Write a story in Nepali for a 5-10 year old child, suitable for difficulty level 2. The theme is 'Culture' (संस्कृति). The story should immerse the reader in a Nepali cultural tradition, like preparing for 'दशैं' by flying kites ('चङ्गा उडाउनु') or celebrating 'तिहार' with 'देउसी-भैलो'. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a914",
            "prompt": "Write a musical story in Nepali for a 3-7 year old child, suitable for difficulty level 2. The theme is 'Music' (संगीत). The story should introduce traditional Nepali musical instruments. Feature characters playing a 'मादल', 'बाँसुरी' (flute), and 'सारंगी' and describe the joyful sounds they make. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a915",
            "prompt": "Write a simple counting story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Numbers' (संख्या). The story should involve a character counting common objects from 'एक' (one) to 'दश' (ten), for example, 'एक सूर्य' (one sun), 'दुई चरा' (two birds). Use repetition for reinforcement. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a916",
            "prompt": "Create a fun story in Nepali for a 3-6 year old child, suitable for difficulty level 1. The theme is 'Alphabet' (वर्णमाला). The story can personify the Nepali letters. For example, 'क' finds a 'कमल' (lotus), and 'ख' chases a 'खरायो' (rabbit). Focus on the sound and a corresponding word for a few letters. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a917",
            "prompt": "Write a 'shape hunt' story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Shapes' (आकार). A character finds shapes in their surroundings: a 'गोलो' (round) roti, a 'त्रिकोण' (triangle) samosa, and a 'वर्ग' (square) window. Keep the language very simple and direct. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a918",
            "prompt": "Write an exciting story in Nepali for a 3-6 year old child, suitable for difficulty level 2. The theme is 'Vehicles' (सवारीसाधन). The story should follow a child observing different vehicles on a Nepali road, like a 'बस', 'ट्याक्सी', and 'मोटरसाइकल'. Include their sounds and simple descriptions of where they are going. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a919",
            "prompt": "Create an informative story in Nepali for a 4-7 year old child, suitable for difficulty level 2. The theme is 'Professions' (पेशा). A child meets people with different jobs common in Nepal, like a 'शिक्षक' (teacher), a 'डाक्टर' (doctor), and a 'किसान' (farmer), and learns how they each help the community. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a91a",
            "prompt": "Write a very basic story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Fruits' (फलफूल). Introduce common Nepali fruits like 'स्याउ' (apple), 'केरा' (banana), and 'सुन्तला' (orange). Describe their color and simple taste ('गुलियो' - sweet) with simple sentences. The focus is on identification. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a91b",
            "prompt": "Create a short story in Nepali for a 2-5 year old child, suitable for difficulty level 1. The theme is 'Vegetables' (तरकारी). The story could be about a visit to a vegetable market ('तरकारी बजार') to identify vegetables like 'आलु' (potato), 'गोलभेडा' (tomato), and 'साग' (leafy greens). The message should be about eating healthy. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a91c",
            "prompt": "Write a gentle, descriptive story in Nepali for a 4-8 year old child, suitable for difficulty level 2. The theme is 'Nature' (प्रकृति). Describe a peaceful scene in the Nepali countryside, mentioning the 'रुख' (trees), 'फूल' (flowers), 'नदी' (river), and 'पहाड' (hills). The story should evoke a sense of calm and wonder. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a91d",
            "prompt": "Write a story in Nepali for a 3-6 year old child, suitable for difficulty level 2. The theme is 'Weather' (मौसम). Describe a day with changing weather: 'घाम लाग्यो' (it's sunny), 'बादल लाग्यो' (it's cloudy), and 'पानी पर्यो' (it's raining). Show what a child does in each weather, connecting it to Nepali life. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a91e",
            "prompt": "Create a simple story for a 3-6 year old child in Nepali, suitable for difficulty level 1. The theme is 'Clothes' (कपडा). The story can describe a child getting dressed, naming items like 'सर्ट' (shirt), 'पाइन्ट' (pants), and a 'टोपी' (hat). Connect the clothes to the weather or an occasion. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a91f",
            "prompt": "Write a vibrant story in Nepali for a 5-10 year old child, suitable for difficulty level 3. The theme is 'Festivals' (चाडपर्व). Describe the excitement and key traditions of a major Nepali festival like 'इन्द्र जात्रा' or 'गाई जात्रा', including specific cultural details and vocabulary. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a920",
            "prompt": "Create a story in Nepali for an 8-12 year old child, suitable for difficulty level 3. The theme is 'Technology' (प्रविधि). The story could be about a child in Nepal using a 'कम्प्युटर' to learn about their culture or using a 'मोबाइल' to talk to relatives abroad, showing technology as a positive tool for connection and learning. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a921",
            "prompt": "Write a curious story in Nepali for a 7-11 year old child, suitable for difficulty level 3. The theme is 'Science' (विज्ञान). Explain a basic scientific concept (like how plants make food or the water cycle) through an engaging narrative of a curious Nepali child who observes and asks questions. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a922",
            "prompt": "Write an imaginative story in Nepali for a 6-10 year old child, suitable for difficulty level 3. The theme is 'Space' (अन्तरिक्ष). The story should follow a child's dream journey to the 'चन्द्रमा' (moon), where they see the 'पृथ्वी' (earth) and the 'ताराहरू' (stars). Introduce simple space vocabulary in a fun, narrative way. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a923",
            "prompt": "Create a gentle story in Nepali for a 3-7 year old child, suitable for difficulty level 2. The theme is 'Feelings' (भावना). Help a child identify simple emotions like 'खुशी' (happy), 'दुःखी' (sad), and 'रिसाएको' (angry) through a character's experiences during their day, ending with a message that all feelings are okay. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a924",
            "prompt": "Write a simple story in Nepali for a 3-6 year old child, suitable for difficulty level 2. The theme is 'Actions' (क्रियाकलाप). Follow a child through their daily routine, describing actions using Nepali verbs like 'उठ्नु' (to wake up), 'खानु' (to eat), 'खेल्नु' (to play), and 'सुत्नु' (to sleep). The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        },
        {
            "theme_id": "686e31e04346d40876c8a925",
            "prompt": "Create a story in Nepali for a 4-8 year old child, suitable for difficulty level 2. The theme is 'Places' (स्थानहरू). Take a character on a visit to important places in a Nepali town, such as a 'विद्यालय' (school), 'अस्पताल' (hospital), 'मन्दिर' (temple), and 'पसल' (shop), explaining the simple purpose of each place. The story must be entirely in Nepali. यो कथा नेपाली बालबालिकाहरूलाई लक्षित गरी, नेपाली शब्द र सन्दर्भहरू प्रयोग गरेर बनाउनुहोस्।"
        }
    ]

    # Step 4: Process each theme one by one
    results = []
    successful_generations = 0
    failed_generations = 0

    print(f"\n🎬 Starting content generation for {len(themes)} themes...")
    print("=" * 60)

    for i, theme in enumerate(themes, 1):
        theme_id_str = str(theme["_id"])
        theme_name = theme.get("name", f"Theme {theme_id_str}")

        print(f"\n[{i}/{len(themes)}] Processing theme: {theme_name}")
        print("-" * 40)

        # Find the prompt for this theme
        prompt_data = next((p for p in story_prompts if p["theme_id"] == theme_id_str), None)

        if prompt_data:
            prompt = prompt_data["prompt"]
            print(f"✅ Found prompt for theme {theme_name}")

            # Generate content for this theme
            result = await generate_content(token, theme_id_str, prompt, theme_name)

            # Store result with theme info
            result_entry = {
                "theme_id": theme_id_str,
                "theme_name": theme_name,
                "prompt_length": len(prompt),
                "result": result,
                "success": "error" not in result
            }
            results.append(result_entry)

            if result_entry["success"]:
                successful_generations += 1
                print(f"🎉 Successfully generated content for {theme_name}")
            else:
                failed_generations += 1
                print(f"💥 Failed to generate content for {theme_name}")

            # Add a small delay between requests to be respectful to the API
            if i < len(themes):
                print("⏳ Waiting 2 seconds before next request...")
                await asyncio.sleep(2)

        else:
            print(f"⚠️  No prompt found for theme {theme_name} (ID: {theme_id_str})")
            failed_generations += 1
            results.append({
                "theme_id": theme_id_str,
                "theme_name": theme_name,
                "prompt_length": 0,
                "result": {"error": "No prompt defined for this theme"},
                "success": False
            })

    # Step 5: Print summary
    print("\n" + "=" * 60)
    print("📊 GENERATION SUMMARY")
    print("=" * 60)
    print(f"✅ Successful generations: {successful_generations}")
    print(f"❌ Failed generations: {failed_generations}")
    print(f"📈 Success rate: {(successful_generations / len(themes) * 100):.1f}%")

    # Print detailed results
    print(f"\n📋 DETAILED RESULTS:")
    print("-" * 60)
    for result in results:
        status = "✅ SUCCESS" if result["success"] else "❌ FAILED"
        print(f"{status} | {result['theme_name']} | Prompt: {result['prompt_length']} chars")
        if not result["success"] and "error" in result["result"]:
            print(f"    Error: {result['result']['error']}")

    print(f"\n🏁 Content generation completed!")
    return results

if __name__ == "__main__":
    asyncio.run(main())